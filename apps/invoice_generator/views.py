import os
import io
import re
import uuid
import json
import zipfile
import smtplib
import tempfile
import pystache
import weasyprint
import pandas as pd
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.shortcuts import render
from django.http import HttpResponse
from email.message import EmailMessage
from rest_framework.views import APIView
from rest_framework import status, generics
from rest_framework.response import Response
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from rest_framework.renderers import StaticHTMLRenderer
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import AllowAny, IsAuthenticated
from .models import CompanyTemplate, SalesInvoiceData, EmailProviderConfig
from .serializers import (
    CompanyTemplateSerializer,
    CompanyTemplateListSerializer,
    EmailProviderConfigSerializer,
    ColumnMappingRequestSerializer,
    ColumnMappingResponseSerializer,
)
from utils.logger import get_logger
from .csv_processor import CSVProcessor

logger = get_logger("invoice_generator.views")

# Create your views here


class InvoiceTemplateListView(APIView):
    """
    API endpoint to fetch all available invoice templates.
    Returns a list of templates with their metadata and raw HTML content.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        templates = []

        # Template metadata
        template_info = {
            "clean_business.html": {
                "name": "Clean Business",
                "description": "A clean and professional business invoice template",
                "preview_image": "https://placehold.co/300x400/059669/ffffff?text=Clean+Business",
            },
            "corporate.html": {
                "name": "Corporate",
                "description": "A formal corporate invoice template with traditional styling",
                "preview_image": "https://placehold.co/300x400/1f2937/ffffff?text=Corporate",
            },
            "minimalist.html": {
                "name": "Minimalist",
                "description": "A simple and elegant minimalist invoice template",
                "preview_image": "https://placehold.co/300x400/64748b/ffffff?text=Minimalist",
            },
            "elegant_classic.html": {
                "name": "Elegant Classic",
                "description": "An elegant classic invoice template with decorative elements",
                "preview_image": "https://placehold.co/300x400/d97706/ffffff?text=Elegant+Classic",
            },
            "contemporary.html": {
                "name": "Contemporary",
                "description": "A modern contemporary invoice template with vibrant colors",
                "preview_image": "https://placehold.co/300x400/7c3aed/ffffff?text=Contemporary",
            },
        }

        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".html"):
                    file_path = os.path.join(templates_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as file:
                        html_content = file.read()

                    template_data = {
                        "id": filename.replace(".html", ""),
                        "filename": filename,
                        "html_content": html_content,
                        **template_info.get(
                            filename,
                            {
                                "name": filename.replace(".html", "")
                                .replace("_", " ")
                                .title(),
                                "description": f"Invoice template: {filename}",
                                "preview_image": "/static/template-previews/default.png",
                            },
                        ),
                    }
                    templates.append(template_data)

            return Response(
                {"success": True, "templates": templates, "count": len(templates)}
            )

        except Exception as e:
            return Response({"success": False, "error": str(e)}, status=500)


class InvoiceTemplateDetailView(APIView):
    """
    API endpoint to fetch a specific invoice template by ID.
    Returns the raw HTML content using StaticHTMLRenderer.
    """

    renderer_classes = [StaticHTMLRenderer]
    permission_classes = [AllowAny]

    def get(self, request, template_id):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{template_id}.html"
        file_path = os.path.join(templates_dir, template_file)

        try:
            if not os.path.exists(file_path):
                return Response(
                    "<html><body><h1>Template not found</h1></body></html>", status=404
                )

            with open(file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            return Response(html_content)

        except Exception as e:
            return Response(
                f"<html><body><h1>Error loading template: {str(e)}</h1></body></html>",
                status=500,
            )


# Company Template Views


class CompanyTemplateListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating company templates.
    """

    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CompanyTemplateListSerializer
        return CompanyTemplateSerializer

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class CompanyTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting company templates.
    """

    serializer_class = CompanyTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        # Update last_used timestamp when template is updated
        serializer.save(last_used=timezone.now())


class CompanyLogoUploadView(APIView):
    """
    API endpoint for uploading company logos.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            if "logo" not in request.FILES:
                return Response(
                    {"error": "No logo file provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logo_file = request.FILES["logo"]

            # Validate file type
            allowed_types = [
                "image/jpeg",
                "image/jpg",
                "image/png",
                "image/gif",
                "image/webp",
            ]
            if logo_file.content_type not in allowed_types:
                return Response(
                    {"error": "Only image files (JPEG, PNG, GIF, WebP) are allowed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate file size (max 5MB)
            if logo_file.size > 5 * 1024 * 1024:
                return Response(
                    {"error": "File size must be less than 5MB"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Generate unique filename
            file_extension = logo_file.name.split(".")[-1].lower()
            unique_filename = f"company_logos/{uuid.uuid4()}.{file_extension}"

            # Save file
            file_path = default_storage.save(
                unique_filename, ContentFile(logo_file.read())
            )
            logo_url = request.build_absolute_uri(settings.MEDIA_URL + file_path)

            return Response(
                {"success": True, "logo_url": logo_url, "file_path": file_path}
            )

        except Exception as e:
            return Response(
                {"error": f"Failed to upload logo: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Sales Invoice Generation Views


class CSVUploadView(APIView):
    """
    API endpoint for uploading CSV/Excel files and detecting columns.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            if "file" not in request.FILES:
                return Response(
                    {"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST
                )

            file = request.FILES["file"]

            # Validate file type - support both CSV and Excel files
            file_extension = file.name.lower().split(".")[-1]
            if file_extension not in ["csv", "xlsx", "xls"]:
                return Response(
                    {
                        "error": "Only CSV and Excel files (.csv, .xlsx, .xls) are allowed"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Use pandas to detect columns from CSV or Excel files
            try:
                if file_extension == "csv":
                    df = pd.read_csv(
                        file, encoding="utf-8", nrows=0
                    )  # Read only headers
                elif file_extension in ["xlsx", "xls"]:
                    df = pd.read_excel(
                        file, nrows=0, engine=None
                    )  # Read only headers with pandas built-in engine

                columns = list(df.columns)
                logger.info(
                    f"Detected columns from {file_extension.upper()} file: {columns}"
                )

            except Exception as e:
                logger.error(f"Error reading file headers: {str(e)}")
                return Response(
                    {"error": f"Failed to read file headers: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Generate suggested mappings
            mapping_result = CSVProcessor.suggest_column_mappings(columns)

            return Response(
                {
                    "success": True,
                    "detected_columns": columns,
                    "suggested_mappings": mapping_result["suggested_mappings"],
                    "confidence_scores": mapping_result["confidence_scores"],
                    "file_type": file_extension.upper(),
                }
            )

        except Exception as e:
            logger.error(f"Error processing file upload: {str(e)}")
            return Response(
                {"error": f"Failed to process file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ColumnMappingSuggestionsView(APIView):
    """
    API endpoint for getting column mapping suggestions.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ColumnMappingRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            csv_columns = serializer.validated_data["csv_columns"]
            mapping_result = CSVProcessor.suggest_column_mappings(csv_columns)

            response_serializer = ColumnMappingResponseSerializer(mapping_result)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"Error generating column mapping suggestions: {str(e)}")
            return Response(
                {"error": f"Failed to generate suggestions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


def get_user_email_folder(user):
    """
    Get the folder name for a user's invoices.
    Use sanitized email or 'anonymous' if user is not authenticated.
    """
    if user and user.is_authenticated and user.email:
        # Sanitize email to use as folder name
        # Replace special characters with underscores
        sanitized = user.email
        special_chars = {
            "@": "_at_",
            ".": "_dot_",
            "+": "_plus_",
            "-": "_dash_",
            "=": "_equals_",
            "#": "_hash_",
            "$": "_dollar_",
            "%": "_percent_",
            "&": "_and_",
            "*": "_star_",
        }
        for char, replacement in special_chars.items():
            sanitized = sanitized.replace(char, replacement)
        return sanitized
    return "anonymous"


def sanitize_filename(filename):
    """
    Sanitize filename to be safe for filesystem.
    """
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', "_", filename)
    # Remove extra spaces and replace with underscores
    filename = re.sub(r"\s+", "_", filename)
    # Remove leading/trailing dots and spaces
    filename = filename.strip(". ")
    # Ensure it's not empty
    if not filename:
        filename = "invoice"
    return filename


class GenerateInvoicesView(APIView):
    """
    API endpoint for generating PDF invoices from CSV/Excel files and template.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            # Get file and other data from request
            file = request.FILES.get("file")
            column_mappings_str = request.data.get("column_mappings", "{}")

            # Get template and company data from request
            company_template_id = request.data.get("company_template_id")
            template_id = request.data.get("template_id")
            temporary_template_data_str = request.data.get("temporary_template_data")

            logger.info(
                f"GenerateInvoicesView - Received request with template_id: {template_id}, company_template_id: {company_template_id}, has_temporary_data: {bool(temporary_template_data_str)}"
            )

            # Parse temporary template data from JSON string if provided
            temporary_template_data = None
            if temporary_template_data_str:
                try:
                    temporary_template_data = (
                        json.loads(temporary_template_data_str)
                        if isinstance(temporary_template_data_str, str)
                        else temporary_template_data_str
                    )
                    logger.info(
                        f"Parsed temporary template data: {temporary_template_data}"
                    )
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse temporary template data: {e}")
                    return Response(
                        {"error": "Invalid temporary template data format"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Parse column mappings from JSON string
            try:
                column_mappings = (
                    json.loads(column_mappings_str) if column_mappings_str else {}
                )
            except json.JSONDecodeError:
                return Response(
                    {"error": "Invalid column mappings format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate required data
            if not file:
                return Response(
                    {"error": "No file provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not column_mappings:
                return Response(
                    {"error": "No column mappings provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Note: company_template_id validation moved to workflow-specific validation below

            # Process CSV/Excel file using pandas
            logger.info(f"Processing file: {file.name} (size: {file.size} bytes)")

            try:
                # Read file based on extension
                file_extension = file.name.lower().split(".")[-1]

                if file_extension == "csv":
                    # Read CSV file
                    df = pd.read_csv(file, encoding="utf-8")
                elif file_extension in ["xlsx", "xls"]:
                    # Read Excel file using pandas built-in engine
                    df = pd.read_excel(
                        file, engine=None
                    )  # Let pandas choose the best engine
                else:
                    return Response(
                        {
                            "error": f"Unsupported file format: {file_extension}. Only CSV and Excel files are supported."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                logger.info(
                    f"Successfully parsed {file_extension.upper()} file with {len(df)} rows and {len(df.columns)} columns"
                )
                logger.info(f"Columns: {list(df.columns)}")

                # Convert DataFrame to list of dictionaries
                csv_data = df.to_dict("records")

                # Clean up any NaN values
                for row in csv_data:
                    for key, value in row.items():
                        if pd.isna(value):
                            row[key] = ""
                        else:
                            row[key] = str(value)

            except Exception as e:
                logger.error(f"Error processing file: {str(e)}")
                return Response(
                    {"error": f"Failed to process file: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get company template data (either from saved template or temporary data)
            company_template = None
            company_data = None

            if temporary_template_data:
                # Use temporary template data passed from frontend
                company_data = temporary_template_data
                final_template_id = template_id or company_data.get("template_id")
                logger.info("Using temporary template data from request")
            elif company_template_id:
                # Use saved company template
                try:
                    company_template = CompanyTemplate.objects.get(
                        id=company_template_id, user=request.user
                    )
                    final_template_id = template_id or company_template.template_id
                    logger.info(
                        f"Using saved company template: {company_template.template_name}"
                    )
                except CompanyTemplate.DoesNotExist:
                    return Response(
                        {"error": "Company template not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
            else:
                return Response(
                    {
                        "error": "Either company_template_id or temporary_template_data is required"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not final_template_id:
                return Response(
                    {"error": "No template ID found in request or template data"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logger.info(f"Using final_template_id: {final_template_id}")

            # Get invoice template HTML
            templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
            template_file = f"{final_template_id}.html"
            template_path = os.path.join(templates_dir, template_file)

            logger.info(f"Looking for template file at: {template_path}")

            if not os.path.exists(template_path):
                # List available templates for debugging
                available_templates = []
                if os.path.exists(templates_dir):
                    available_templates = [
                        f for f in os.listdir(templates_dir) if f.endswith(".html")
                    ]

                logger.error(
                    f"Template file not found: {template_path}. Available templates: {available_templates}"
                )
                return Response(
                    {
                        "error": f"Invoice template '{final_template_id}' not found",
                        "available_templates": [
                            t.replace(".html", "") for t in available_templates
                        ],
                        "requested_template": final_template_id,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            with open(template_path, "r", encoding="utf-8") as file:
                template_html = file.read()

            logger.info(f"Successfully loaded template: {final_template_id}")

            # Group CSV data by invoice number to create one PDF per invoice
            invoices_by_number = {}

            logger.info(f"Processing {len(csv_data)} rows of CSV data")

            # First pass: Group rows by invoice number
            for row_index, row_data in enumerate(csv_data):
                try:
                    # Map CSV data to invoice fields for this row
                    mapped_row = {}
                    for field, csv_column in column_mappings.items():
                        mapped_row[field] = row_data.get(csv_column, "")

                    invoice_number = mapped_row.get(
                        "invoice_number", f"INV_{row_index + 1:03d}"
                    )

                    if invoice_number not in invoices_by_number:
                        invoices_by_number[invoice_number] = {
                            "invoice_data": mapped_row.copy(),
                            "line_items": [],
                        }
                        # Remove line item specific fields from main invoice data
                        for field in [
                            "service_description",
                            "quantity",
                            "unit_rate",
                            "line_amount",
                        ]:
                            if (
                                field
                                in invoices_by_number[invoice_number]["invoice_data"]
                            ):
                                del invoices_by_number[invoice_number]["invoice_data"][
                                    field
                                ]

                    # Add this row as a line item
                    line_item = {
                        "service_description": mapped_row.get(
                            "service_description", ""
                        ),
                        "quantity": mapped_row.get("quantity", "1"),
                        "unit_rate": mapped_row.get("unit_rate", "0.00"),
                        "line_amount": mapped_row.get("line_amount", "0.00"),
                        "tax_percent": mapped_row.get("tax_percent", "0"),
                        "tax_value": mapped_row.get("tax_value", "0.00"),
                        "discount_amount": mapped_row.get("discount_amount", "0.00"),
                    }
                    invoices_by_number[invoice_number]["line_items"].append(line_item)

                except Exception as e:
                    logger.error(f"Error processing row {row_index + 1}: {str(e)}")
                    continue

            logger.info(f"Grouped data into {len(invoices_by_number)} unique invoices")

            # Second pass: Generate PDFs for each unique invoice
            generated_invoices = []
            user_folder = get_user_email_folder(request.user)

            for invoice_number, invoice_group in invoices_by_number.items():
                try:
                    logger.info(f"Generating PDF for invoice: {invoice_number}")

                    invoice_data = invoice_group["invoice_data"]
                    line_items = invoice_group["line_items"]

                    # Add company data to invoice (from either saved template or temporary data)
                    if company_template:
                        # Use saved company template
                        company_info = {
                            "company_name": company_template.company_name,
                            "company_address_line_1": company_template.address_line_1,
                            "company_address_line_2": company_template.address_line_2,
                            "company_city": company_template.city,
                            "company_state_province": company_template.state_province,
                            "company_postal_code": company_template.postal_code,
                            "company_country": company_template.country,
                            "company_phone": company_template.phone,
                            "company_email": company_template.email,
                            "company_website": company_template.website,
                            "payment_terms": company_template.default_payment_terms,
                            "bank_name": company_template.bank_name,
                            "account_number": company_template.account_number,
                            "routing_number": company_template.routing_number,
                            "swift_code": company_template.swift_code,
                            "tax_id": company_template.tax_id,
                            "business_registration": company_template.business_registration,
                            "company_logo_url": company_template.logo_url,
                            "has_logo": bool(company_template.logo_url),
                        }
                    elif company_data:
                        # Use temporary company data
                        company_info = {
                            "company_name": company_data.get("company_name", ""),
                            "company_address_line_1": company_data.get(
                                "address_line_1", ""
                            ),
                            "company_address_line_2": company_data.get(
                                "address_line_2", ""
                            ),
                            "company_city": company_data.get("city", ""),
                            "company_state_province": company_data.get(
                                "state_province", ""
                            ),
                            "company_postal_code": company_data.get("postal_code", ""),
                            "company_country": company_data.get("country", ""),
                            "company_phone": company_data.get("phone", ""),
                            "company_email": company_data.get("email", ""),
                            "company_website": company_data.get("website", ""),
                            "payment_terms": company_data.get(
                                "default_payment_terms", ""
                            ),
                            "bank_name": company_data.get("bank_name", ""),
                            "account_number": company_data.get("account_number", ""),
                            "routing_number": company_data.get("routing_number", ""),
                            "swift_code": company_data.get("swift_code", ""),
                            "tax_id": company_data.get("tax_id", ""),
                            "business_registration": company_data.get(
                                "business_registration", ""
                            ),
                            "company_logo_url": company_data.get("logo_url"),
                            "has_logo": bool(company_data.get("logo_url")),
                        }
                    else:
                        # Fallback - no company data available
                        company_info = {
                            "company_name": "",
                            "company_address_line_1": "",
                            "company_address_line_2": "",
                            "company_city": "",
                            "company_state_province": "",
                            "company_postal_code": "",
                            "company_country": "",
                            "company_phone": "",
                            "company_email": "",
                            "company_website": "",
                            "payment_terms": "",
                            "bank_name": "",
                            "account_number": "",
                            "routing_number": "",
                            "swift_code": "",
                            "tax_id": "",
                            "business_registration": "",
                            "company_logo_url": None,
                            "has_logo": False,
                        }

                    invoice_data.update(company_info)

                    # Add current date if not provided
                    if not invoice_data.get("bill_date"):
                        invoice_data["bill_date"] = datetime.now().strftime("%Y-%m-%d")

                    # Calculate totals from line items
                    subtotal = sum(
                        float(item.get("line_amount", 0)) for item in line_items
                    )
                    total_tax = sum(
                        float(item.get("tax_value", 0)) for item in line_items
                    )
                    total_discount = sum(
                        float(item.get("discount_amount", 0)) for item in line_items
                    )
                    total_amount = subtotal + total_tax - total_discount

                    # Add calculated totals to invoice data
                    invoice_data.update(
                        {
                            "subtotal": f"{subtotal:.2f}",
                            "total_tax": f"{total_tax:.2f}",
                            "total_discount": f"{total_discount:.2f}",
                            "total_amount": f"{total_amount:.2f}",
                            "line_items": line_items,
                        }
                    )

                    # For backward compatibility with single-item templates, use first line item data
                    if line_items:
                        first_item = line_items[0]
                        invoice_data.update(
                            {
                                "service_description": first_item.get(
                                    "service_description", ""
                                ),
                                "quantity": first_item.get("quantity", "1"),
                                "unit_rate": first_item.get("unit_rate", "0.00"),
                                "line_amount": first_item.get("line_amount", "0.00"),
                                "tax_percent": first_item.get("tax_percent", "0"),
                                "tax_value": first_item.get("tax_value", "0.00"),
                                "discount_amount": first_item.get(
                                    "discount_amount", "0.00"
                                ),
                            }
                        )

                    # Render template with data
                    rendered_html = pystache.render(template_html, invoice_data)

                    # Generate PDF
                    pdf_bytes = weasyprint.HTML(string=rendered_html).write_pdf()

                    if pdf_bytes is None:
                        logger.error(
                            "Failed to generate PDF - weasyprint returned None"
                        )
                        continue

                    # Create filename with invoice number and current date/time in milliseconds
                    client_name = sanitize_filename(
                        invoice_data.get("client_company", "Unknown_Client")
                    )
                    invoice_number_clean = sanitize_filename(invoice_number)

                    # Generate filename with current date/time in milliseconds
                    import time

                    current_time_ms = int(time.time() * 1000)
                    filename = f"{invoice_number_clean}_-_{current_time_ms}.pdf"

                    # Create folder structure: invoice_pdfs/{user_folder}/{client_name}/
                    client_folder = sanitize_filename(client_name)
                    file_path = f"invoice_pdfs/{user_folder}/{client_folder}/{filename}"

                    # Save PDF to storage
                    saved_path = default_storage.save(file_path, ContentFile(pdf_bytes))

                    # Fix download URL construction - use the path after invoice_pdfs/
                    download_path = saved_path
                    if download_path.startswith("invoice_pdfs/"):
                        download_path = download_path[len("invoice_pdfs/") :]

                    generated_invoices.append(
                        {
                            "client_name": invoice_data.get(
                                "client_company", "Unknown Client"
                            ),
                            "client_email": invoice_data.get("contact_email", ""),
                            "invoice_number": invoice_number,
                            "filename": filename,
                            "file_path": saved_path,
                            "download_url": f"{settings.BACKEND_URL}/api/v1/invoice-generator/download-invoice/{download_path}/",
                            "line_items_count": len(line_items),
                            "total_amount": f"{total_amount:.2f}",
                        }
                    )

                except Exception as e:
                    logger.error(
                        f"Error generating invoice for {invoice_number}: {str(e)}"
                    )
                    continue

            logger.info(f"Successfully generated {len(generated_invoices)} invoices")

            # Create a session-specific identifier for this batch of invoices
            session_id = str(uuid.uuid4())

            # Store the file paths for this session in the response
            # The frontend will use these paths to download only the current batch
            file_paths = [invoice["file_path"] for invoice in generated_invoices]

            return Response(
                {
                    "success": True,
                    "generated_invoices": generated_invoices,
                    "total_generated": len(generated_invoices),
                    "session_id": session_id,
                    "file_paths": file_paths,
                    "zip_download_url": f"{settings.BACKEND_URL}/api/v1/invoice-generator/download-invoices-zip/{user_folder}/",
                }
            )

        except Exception as e:
            logger.error(f"Error in GenerateInvoicesView: {str(e)}")
            logger.error(f"Request data: {request.data}")
            return Response(
                {"error": f"Failed to generate invoices: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DownloadInvoiceView(APIView):
    """
    API endpoint for downloading individual invoice PDFs.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, file_path):
        try:
            # Reconstruct full file path
            full_path = f"invoice_pdfs/{file_path}"

            # Check if file exists
            if not default_storage.exists(full_path):
                return Response(
                    {"error": "Invoice file not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get the file
            file_obj = default_storage.open(full_path, "rb")
            file_content = file_obj.read()
            file_obj.close()

            # Extract filename from path and get just the invoice number
            stored_filename = os.path.basename(full_path)

            # Extract invoice number from filename (format: {invoice_number}_-_{timestamp}.pdf)
            if "_-_" in stored_filename:
                invoice_number = stored_filename.split("_-_")[0]
                download_filename = f"{invoice_number}.pdf"
            else:
                # Fallback to original filename if format doesn't match
                download_filename = stored_filename

            # Create response
            response = HttpResponse(file_content, content_type="application/pdf")
            response["Content-Disposition"] = (
                f'attachment; filename="{download_filename}"'
            )
            return response

        except Exception as e:
            logger.error(f"Error downloading invoice: {str(e)}")
            return Response(
                {"error": f"Failed to download invoice: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DownloadInvoicesZipView(APIView):
    """
    API endpoint for downloading specific invoices as a ZIP file.
    Supports both GET (all invoices) and POST (specific file paths) methods.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, user_folder):
        """Download all invoices for the user (legacy behavior)."""
        return self._create_zip_response(request, user_folder, file_paths=None)

    def post(self, request, user_folder):
        """Download specific invoices based on provided file paths."""
        file_paths = request.data.get("file_paths", [])
        if not file_paths:
            return Response(
                {"error": "No file paths provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return self._create_zip_response(request, user_folder, file_paths=file_paths)

    def _create_zip_response(self, request, user_folder, file_paths=None):
        """Create ZIP file with specified invoices or all invoices."""
        try:
            # Verify user folder matches current user
            current_user_folder = get_user_email_folder(request.user)
            if user_folder != current_user_folder:
                return Response(
                    {"error": "Access denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Create temporary file for ZIP
            with tempfile.NamedTemporaryFile(delete=False, suffix=".zip") as temp_zip:
                with zipfile.ZipFile(temp_zip, "w", zipfile.ZIP_DEFLATED) as zip_file:

                    if file_paths:
                        # Add only specific files (current session invoices)
                        for file_path in file_paths:
                            if default_storage.exists(file_path):
                                try:
                                    file_obj = default_storage.open(file_path, "rb")
                                    file_content = file_obj.read()
                                    file_obj.close()

                                    # Extract client folder and filename to maintain hierarchy
                                    # file_path format: invoice_pdfs/{user_folder}/{client_name}/{filename}
                                    path_parts = file_path.split("/")
                                    if len(path_parts) >= 3:
                                        client_folder = path_parts[
                                            -2
                                        ]  # Second to last part
                                        stored_filename = path_parts[-1]  # Last part

                                        # Extract invoice number from filename for ZIP
                                        if "_-_" in stored_filename:
                                            invoice_number = stored_filename.split(
                                                "_-_"
                                            )[0]
                                            zip_filename = f"{invoice_number}.pdf"
                                        else:
                                            # Fallback to original filename if format doesn't match
                                            zip_filename = stored_filename

                                        zip_path = f"{client_folder}/{zip_filename}"
                                    else:
                                        # Fallback if path structure is unexpected
                                        stored_filename = os.path.basename(file_path)
                                        if "_-_" in stored_filename:
                                            invoice_number = stored_filename.split(
                                                "_-_"
                                            )[0]
                                            zip_path = f"{invoice_number}.pdf"
                                        else:
                                            zip_path = stored_filename

                                    # Add to ZIP with client folder structure
                                    zip_file.writestr(zip_path, file_content)

                                except Exception as e:
                                    logger.warning(
                                        f"Error processing file {file_path}: {str(e)}"
                                    )
                                    continue
                            else:
                                logger.warning(f"File not found: {file_path}")
                    else:
                        # Add all PDF files from all client folders (legacy behavior)
                        user_invoice_dir = f"invoice_pdfs/{user_folder}/"
                        try:
                            dirs, files = default_storage.listdir(user_invoice_dir)

                            for client_dir in dirs:
                                client_path = f"{user_invoice_dir}{client_dir}/"
                                try:
                                    _, client_files = default_storage.listdir(
                                        client_path
                                    )
                                    for file_name in client_files:
                                        if file_name.endswith(".pdf"):
                                            file_path = f"{client_path}{file_name}"
                                            if default_storage.exists(file_path):
                                                file_obj = default_storage.open(
                                                    file_path, "rb"
                                                )
                                                file_content = file_obj.read()
                                                file_obj.close()

                                                # Extract invoice number from filename for ZIP
                                                if "_-_" in file_name:
                                                    invoice_number = file_name.split(
                                                        "_-_"
                                                    )[0]
                                                    zip_filename = (
                                                        f"{invoice_number}.pdf"
                                                    )
                                                else:
                                                    # Fallback to original filename if format doesn't match
                                                    zip_filename = file_name

                                                # Add to ZIP with client folder structure
                                                zip_file.writestr(
                                                    f"{client_dir}/{zip_filename}",
                                                    file_content,
                                                )
                                except Exception as e:
                                    logger.warning(
                                        f"Error processing client folder {client_dir}: {str(e)}"
                                    )
                                    continue

                        except Exception as e:
                            logger.error(f"Error listing invoice directory: {str(e)}")
                            return Response(
                                {"error": "No invoices found"},
                                status=status.HTTP_404_NOT_FOUND,
                            )

                temp_zip_path = temp_zip.name

            # Read the ZIP file
            with open(temp_zip_path, "rb") as zip_file:
                zip_content = zip_file.read()

            # Clean up temporary file
            os.unlink(temp_zip_path)

            # Create response
            response = HttpResponse(zip_content, content_type="application/zip")
            filename_suffix = "current_batch" if file_paths else "all"
            response["Content-Disposition"] = (
                f'attachment; filename="invoices_{filename_suffix}_{user_folder}.zip"'
            )
            return response

        except Exception as e:
            logger.error(f"Error creating ZIP file: {str(e)}")
            return Response(
                {"error": f"Failed to create ZIP file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SendInvoiceEmailsView(APIView):
    """
    API endpoint for sending invoice emails to customers.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Get data from request
            invoice_data = request.data.get("invoice_data", {})
            email_template = request.data.get("email_template", {})
            recipient_email = request.data.get("recipient_email", "")

            logger.info(f"SendInvoiceEmailsView - Sending email to: {recipient_email}")

            # Validate required fields
            if not invoice_data:
                return Response(
                    {"error": "Invoice data is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not email_template:
                return Response(
                    {"error": "Email template is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not recipient_email:
                return Response(
                    {"error": "Recipient email is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate email format
            import re

            email_regex = r"^[^\s@]+@[^\s@]+\.[^\s@]+$"
            if not re.match(email_regex, recipient_email):
                return Response(
                    {"error": "Invalid email format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Extract email template data
            email_subject = email_template.get("emailSubject", "Your Invoice")
            email_body = email_template.get(
                "emailBody", "Please find your invoice attached."
            )
            sender_email = email_template.get("senderEmail", "")

            # Replace template variables with actual data
            rendered_subject = self._render_email_template(email_subject, invoice_data)
            rendered_body = self._render_email_template(email_body, invoice_data)

            # Send the email
            success = self._send_invoice_email(
                recipient_email=recipient_email,
                subject=rendered_subject,
                body=rendered_body,
                sender_email=sender_email,
                invoice_data=invoice_data,
            )

            if success:
                return Response(
                    {
                        "success": True,
                        "message": f"Email sent successfully to {recipient_email}",
                    }
                )
            else:
                return Response(
                    {"error": "Failed to send email"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error in SendInvoiceEmailsView: {str(e)}")
            return Response(
                {"error": f"Failed to send email: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _render_email_template(self, template_text, invoice_data):
        """
        Replace template variables with actual invoice data.
        """
        rendered_text = template_text

        # Replace common variables
        replacements = {
            "{{customer_name}}": invoice_data.get("client_name", "Valued Customer"),
            "{{customer}}": invoice_data.get("client_name", "Valued Customer"),
            "{{invoice_number}}": invoice_data.get("invoice_number", "N/A"),
            "{{total_amount}}": invoice_data.get("total_amount", "0.00"),
            "{{company}}": "Your Company",  # This could be made dynamic
            "{{signature}}": "Best regards",  # This could be made dynamic
        }

        for placeholder, value in replacements.items():
            rendered_text = rendered_text.replace(placeholder, str(value))

        return rendered_text

    def _send_invoice_email(
        self, recipient_email, subject, body, sender_email, invoice_data
    ):
        """
        Send email with invoice attachment using the same email infrastructure as tax_acc_assistant.
        """
        try:
            # Load email configuration from environment variables
            from_email = os.getenv("EMAIL_HOST_USER")
            app_password = os.getenv("EMAIL_HOST_PASSWORD")
            smtp_server = os.getenv("EMAIL_HOST", "smtp.hostinger.com")
            smtp_port = int(os.getenv("EMAIL_PORT", "587"))
            email_enabled = os.getenv("EMAIL_ENABLED", "True").lower() == "true"
            email_test_mode = os.getenv("EMAIL_TEST_MODE", "False").lower() == "true"

            # Check if email is enabled
            if not email_enabled:
                logger.info("Email sending is disabled by configuration")
                return False

            # Check if credentials are available
            if not from_email or not app_password:
                logger.error("Email credentials not available")
                return False

            # Use sender email if provided, otherwise use default
            actual_from_email = sender_email if sender_email else from_email

            # Create the email message
            msg = EmailMessage()
            msg["Subject"] = subject
            msg["From"] = actual_from_email
            msg["To"] = recipient_email

            # Add the body
            msg.set_content(body)

            # Try to attach the invoice PDF if available
            if invoice_data.get("file_path"):
                try:
                    file_path = invoice_data["file_path"]
                    if default_storage.exists(file_path):
                        with default_storage.open(file_path, "rb") as pdf_file:
                            pdf_content = pdf_file.read()

                            # Extract invoice number from stored filename for email attachment
                            stored_filename = invoice_data.get(
                                "filename", "invoice.pdf"
                            )
                            if "_-_" in stored_filename:
                                invoice_number = stored_filename.split("_-_")[0]
                                email_filename = f"{invoice_number}.pdf"
                            else:
                                # Fallback to original filename if format doesn't match
                                email_filename = stored_filename

                            msg.add_attachment(
                                pdf_content,
                                maintype="application",
                                subtype="pdf",
                                filename=email_filename,
                            )
                except Exception as e:
                    logger.warning(f"Could not attach invoice PDF: {str(e)}")

            # Log SMTP configuration
            logger.info(
                f"Sending email to {recipient_email} using SMTP server {smtp_server}:{smtp_port}"
            )

            # If in test mode, just log the email instead of sending it
            if email_test_mode:
                logger.info(
                    f"TEST MODE: Would send email to {recipient_email} with subject '{subject}'"
                )
                return True

            # Send the email using SMTP
            with smtplib.SMTP(smtp_server, smtp_port) as smtp:
                smtp.starttls()
                smtp.login(from_email, app_password)
                smtp.send_message(msg)
                logger.info(f"Email sent successfully to {recipient_email}")
                return True

        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            return False


# MS Graph API Views
class MSGraphAuthView(APIView):
    """
    API endpoint for Microsoft Graph authentication and account management.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Handle MS Graph authentication and save account info."""
        try:
            access_token = request.data.get("access_token")
            account_info = request.data.get("account_info", {})

            if not access_token:
                return Response(
                    {"error": "Access token is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Import here to avoid circular imports
            from .services.ms_graph_service import MSGraphService

            # Validate the token by getting user profile
            graph_service = MSGraphService(access_token)
            user_profile = graph_service.get_user_profile()

            if not user_profile:
                return Response(
                    {"error": "Invalid access token or failed to get user profile"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Update account info with profile data
            account_info.update(
                {
                    "userPrincipalName": user_profile.get("userPrincipalName"),
                    "displayName": user_profile.get("displayName"),
                    "mail": user_profile.get("mail"),
                    "id": user_profile.get("id"),
                }
            )

            return Response(
                {
                    "success": True,
                    "user_profile": user_profile,
                    "account_info": account_info,
                }
            )

        except Exception as e:
            logger.error(f"Error in MSGraphAuthView: {str(e)}")
            return Response(
                {"error": f"Authentication failed: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MSGraphAccountsView(APIView):
    """
    API endpoint for managing user's MS Graph accounts.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user's saved MS Graph accounts."""
        try:
            # Get provider from query params, default to 'outlook'
            provider = request.query_params.get("provider", "")

            # Get valid providers from the model
            valid_providers = [
                choice[0] for choice in EmailProviderConfig.PROVIDER_CHOICES
            ]

            # Validate provider
            if provider not in valid_providers:
                return Response(
                    {
                        "error": f"Invalid provider. Supported providers are: {', '.join(valid_providers)}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            configs = EmailProviderConfig.objects.filter(
                user=request.user, provider=provider, is_active=True
            )

            serializer = EmailProviderConfigSerializer(configs, many=True)
            return Response({"success": True, "accounts": serializer.data})

        except Exception as e:
            logger.error(f"Error getting MS Graph accounts: {str(e)}")
            return Response(
                {"error": f"Failed to get accounts: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MSGraphSendTestEmailView(APIView):
    """
    API endpoint for sending test emails via MS Graph.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Send a test email using MS Graph API or Gmail API."""
        try:
            access_token = request.data.get("access_token")
            from_email = request.data.get("from_email")
            to_email = request.data.get("to_email")
            account_info = request.data.get("account_info", {})
            provider = request.data.get(
                "provider", "outlook"
            )  # Default to outlook for backward compatibility
            config_id = request.data.get("config_id")
            use_stored_config = request.data.get("use_stored_config", False)

            # For stored configs, we don't need access_token
            if use_stored_config and config_id:
                if not all([from_email, to_email]):
                    return Response(
                        {
                            "error": "from_email and to_email are required for stored config"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # For Gmail stored configs, we need to prompt for re-authentication
                # since Gmail access tokens expire quickly and we don't store refresh tokens
                if provider == "gmail":
                    logger.info(
                        f"Gmail stored config requires re-authentication for {from_email} to {to_email}"
                    )
                    return Response(
                        {
                            "success": False,
                            "error": "gmail_reauth_required",
                            "message": "Please re-authenticate with Gmail to send test email",
                            "provider": provider,
                            "from_email": from_email,
                            "to_email": to_email,
                            "requires_reauth": True,
                        },
                        status=status.HTTP_401_UNAUTHORIZED,
                    )

                # For Outlook stored configs, continue with existing logic
                # (Outlook has longer-lived tokens or refresh token support)

            elif not all([access_token, from_email, to_email]):
                return Response(
                    {"error": "Access token, from_email, and to_email are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Import here to avoid circular imports
            if provider == "gmail":
                from .services.gmail_service import GmailService

                email_service = GmailService(access_token)
            else:
                from .services.ms_graph_service import MSGraphService

                email_service = MSGraphService(access_token)

            # Send test email
            success = email_service.send_test_email(to_email, from_email)

            if success:
                logger.info(
                    f"Test email sent successfully from {from_email} to {to_email} via {provider}"
                )
                return Response(
                    {
                        "success": True,
                        "message": "Test email sent successfully!",
                        "provider": provider,
                        "from_email": from_email,
                        "to_email": to_email,
                    }
                )
            else:
                return Response(
                    {"error": "Failed to send test email"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error sending test email: {str(e)}")
            return Response(
                {"error": f"Failed to send test email: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MSGraphSaveConfigView(APIView):
    """
    API endpoint for saving MS Graph email configuration.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Save MS Graph email configuration."""
        try:
            account_info = request.data.get("account_info", {})
            provider = request.data.get("provider", "outlook")

            if not account_info:
                return Response(
                    {"error": "Account info is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if configuration already exists
            existing_config = EmailProviderConfig.objects.filter(
                user=request.user,
                provider=provider,
                account_info__username=account_info.get("username"),
            ).first()

            if existing_config:
                # Update existing configuration
                existing_config.account_info = account_info
                existing_config.is_active = True
                existing_config.save()

                serializer = EmailProviderConfigSerializer(existing_config)
                return Response(
                    {
                        "success": True,
                        "message": "Email configuration updated successfully",
                        "config": serializer.data,
                    }
                )
            else:
                # Create new configuration
                config_data = {
                    "provider": provider,
                    "account_info": account_info,
                    "is_active": True,
                }

                serializer = EmailProviderConfigSerializer(
                    data=config_data, context={"request": request}
                )

                if serializer.is_valid():
                    config = serializer.save()
                    return Response(
                        {
                            "success": True,
                            "message": "Email configuration saved successfully",
                            "config": serializer.data,
                        }
                    )
                else:
                    return Response(
                        {"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
                    )

        except Exception as e:
            logger.error(f"Error saving MS Graph config: {str(e)}")
            return Response(
                {"error": f"Failed to save configuration: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
